# 🚀 Guia de Deploy para Produção

## Problema Resolvido: CORS Error

O erro que você estava vendo:
```
Access to script at 'https://www.swingcuritiba.com.br/build/app-CWeen5Pw.js' from origin 'https://swingcuritiba.com.br' has been blocked by CORS policy
```

Foi causado porque:
- Você acessa o site em `https://swingcuritiba.com.br` (sem www)
- Mas o `.env.production` estava configurado para `https://www.swingcuritiba.com.br` (com www)
- Isso criava um conflito de CORS entre domínios diferentes

## ✅ Correções Implementadas

### 1. **Configuração do Vite**
- Detecta automaticamente ambiente de produção
- Usa configurações diferentes para dev e produção
- Remove TLS detection em produção

### 2. **Arquivo .env.production Corrigido**
```env
APP_URL=https://swingcuritiba.com.br
ASSET_URL=https://swingcuritiba.com.br
```

### 3. **Assets Compilados**
- Build otimizado para produção
- Manifest.json gerado corretamente
- Todos os arquivos JS/CSS minificados

## 🔧 Como Fazer o Deploy

### Opção 1: Script Automático (Recomendado)
```powershell
.\deploy.ps1
```

### Opção 2: Manual

1. **Preparar ambiente:**
```bash
# Copiar configurações de produção
cp .env.production .env

# Limpar cache
php artisan config:clear
php artisan cache:clear

# Compilar assets
npm run build

# Otimizar para produção
php artisan config:cache
php artisan route:cache
```

2. **Upload via FTP:**
Faça upload destes arquivos/pastas:
- `app/`
- `bootstrap/`
- `config/`
- `database/`
- `public/` (incluindo `public/build/`)
- `resources/views/`
- `routes/`
- `storage/app/`
- `storage/framework/`
- `.env`
- `artisan`
- `composer.json`
- `composer.lock`

3. **No servidor (via SSH ou painel):**
```bash
# Executar migrações
php artisan migrate --force

# Configurar permissões
chmod -R 755 storage bootstrap/cache
chmod -R 777 storage/logs
```

## 🌐 Configurações de Domínio

### Certifique-se de que:
- O domínio principal é `swingcuritiba.com.br` (sem www)
- Se houver redirecionamento, deve ser de `www.` para sem `www`
- SSL/HTTPS está ativo

### Configuração no .htaccess (se necessário):
```apache
# Redirecionar www para sem www
RewriteEngine On
RewriteCond %{HTTP_HOST} ^www\.swingcuritiba\.com\.br [NC]
RewriteRule ^(.*)$ https://swingcuritiba.com.br/$1 [R=301,L]
```

## 📧 Configurações de Email (KingHost)

Já configurado no `.env.production`:
```env
MAIL_MAILER=smtp
MAIL_HOST=smtp.kinghost.net
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=GodTei45!cva
MAIL_ENCRYPTION=tls
```

## 🔍 Verificações Pós-Deploy

1. **Teste o site:** https://swingcuritiba.com.br
2. **Verifique assets:** Abra DevTools e confirme que não há erros CORS
3. **Teste funcionalidades:**
   - Login/Registro
   - Upload de fotos
   - Live streaming
   - Envio de emails

## 🔄 Voltar ao Desenvolvimento

Após o deploy, para voltar ao desenvolvimento local:
```powershell
Copy-Item '.env.backup' '.env'
php artisan config:clear
npm run dev
```

## 🆘 Troubleshooting

### Se ainda houver erro CORS:
1. Verifique se o domínio no navegador é exatamente `swingcuritiba.com.br`
2. Limpe cache do navegador (Ctrl+Shift+R)
3. Verifique se o `.env` no servidor tem as URLs corretas

### Se assets não carregarem:
1. Verifique se a pasta `public/build/` foi enviada
2. Confirme que `manifest.json` existe
3. Verifique permissões da pasta `public/`

### Se houver erro 500:
1. Verifique logs em `storage/logs/`
2. Confirme que `APP_KEY` está definida
3. Execute `php artisan config:cache` no servidor
