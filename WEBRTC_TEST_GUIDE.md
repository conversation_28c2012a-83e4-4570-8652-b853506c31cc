# 🎥 G<PERSON><PERSON> de <PERSON>e - WebRTC Live Streaming

## 🔧 Problemas Corrigidos

### ✅ **1. Erro JavaScript "scrollChatToBottom is not defined"**
- **Problema**: Função não estava no escopo global
- **Solução**: Movida para `window.scrollChatToBottom`

### ✅ **2. WebRTC não funcionando para viewers**
- **Problema**: Conexão P2P não estabelecida corretamente
- **Solução**: Melhorado sistema de conexão com múltiplos servidores

## 🧪 Como Testar o WebRTC

### **Passo 1: Preparar o Teste**
1. Abra **2 navegadores diferentes** (ou abas anônimas)
2. Faça login com **usuários diferentes** em cada navegador
3. Certifique-se de que a câmera/microfone estão funcionando

### **Passo 2: Iniciar Broadcast**
**No Navegador 1 (Broadcaster):**
1. Acesse: `/lives/transmitir`
2. Clique em "Criar Nova Live"
3. Preencha título e descrição
4. <PERSON><PERSON> em "Câmera WebRTC" (botão azul)
5. Per<PERSON><PERSON> acesso à câmera/microfone
6. Clique em "Iniciar Live"
7. **Verifique no console**: deve aparecer logs como:
   ```
   ✅ Peer conectado com ID: live-123
   📹 Stream iniciado
   🔗 Aguardando viewers...
   ```

### **Passo 3: Conectar como Viewer**
**No Navegador 2 (Viewer):**
1. Acesse: `/lives` (lista de lives ativas)
2. Clique na live que foi criada
3. **Verifique no console**: deve aparecer logs como:
   ```
   🔗 Tentando conectar como viewer ao stream: live-123
   ✅ Viewer conectado com ID: viewer-abc123
   📹 Stream recebido do broadcaster!
   ```

### **Passo 4: Verificar Conexão**
**No Broadcaster (Navegador 1):**
- Console deve mostrar:
  ```
  🔗 Recebendo chamada de viewer: viewer-abc123
  📹 Respondendo chamada com stream local
  ✅ Viewer conectado com sucesso: viewer-abc123
  👥 Total de viewers conectados: 1
  ```

**No Viewer (Navegador 2):**
- Deve ver o vídeo do broadcaster
- Chat deve funcionar
- Contador de viewers deve atualizar

## 🐛 Troubleshooting

### **Problema: "Timeout na conexão"**
**Possíveis causas:**
- Firewall bloqueando WebRTC
- Rede corporativa restritiva
- Servidor PeerJS indisponível

**Soluções:**
1. Teste em rede doméstica
2. Use VPN se necessário
3. Verifique console para erros específicos

### **Problema: "Não foi possível iniciar chamada"**
**Possíveis causas:**
- Broadcaster não está online
- IDs não coincidem
- Problema de sincronização

**Soluções:**
1. Recarregue ambas as páginas
2. Verifique se a live está realmente ativa
3. Teste com IDs diferentes

### **Problema: "Stream não aparece"**
**Possíveis causas:**
- Permissões de câmera negadas
- Codec não suportado
- Problema de NAT/Firewall

**Soluções:**
1. Verifique permissões do navegador
2. Teste em localhost primeiro
3. Use navegadores atualizados

## 📊 Logs Importantes

### **Broadcaster deve mostrar:**
```javascript
🚀 Inicializando streaming service para: live-123
✅ Peer conectado com ID: live-123
📹 Câmera iniciada com sucesso
🔗 Recebendo chamada de viewer: viewer-abc123
✅ Viewer conectado com sucesso: viewer-abc123
👥 Total de viewers conectados: 1
```

### **Viewer deve mostrar:**
```javascript
=== Viewer conectado à live: 123
🔗 Tentando conectar como viewer ao stream: live-123
✅ Viewer conectado com ID: viewer-abc123
📹 Stream recebido do broadcaster!
```

## 🌐 Configurações de Rede

### **Portas necessárias:**
- **443** (HTTPS/WSS)
- **Portas UDP aleatórias** para WebRTC

### **Servidores STUN utilizados:**
- `stun:stun.l.google.com:19302`
- `stun:stun1.l.google.com:19302`
- `stun:stun2.l.google.com:19302`
- `stun:stun3.l.google.com:19302`
- `stun:stun4.l.google.com:19302`

### **Servidores PeerJS (fallback):**
1. `peerjs-server.herokuapp.com` (primário)
2. `0.peerjs.com` (secundário)

## 🔄 Próximos Passos

Se o teste básico funcionar:
1. ✅ **WebRTC está configurado corretamente**
2. ✅ **Múltiplos viewers podem se conectar**
3. ✅ **Sistema está pronto para produção**

Se houver problemas:
1. 🔍 **Verificar logs do console**
2. 🌐 **Testar conectividade de rede**
3. 🔧 **Ajustar configurações de firewall**

## 📞 Suporte

Em caso de problemas persistentes:
1. Copie os logs do console
2. Informe navegadores utilizados
3. Descreva o comportamento observado
4. Teste em diferentes redes se possível
