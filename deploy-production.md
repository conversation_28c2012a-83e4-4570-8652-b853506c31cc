# 🚀 Deploy para Produção - www.swingcuritiba.com.br

## 📋 Checklist de Deploy

### ✅ **1. Preparar Arquivos para Upload**

#### **Arquivos JavaScript Compilados:**
```bash
npm run build
```

#### **Arquivos que devem ser enviados via FTP:**
- `public/build/` (todos os arquivos compilados) ⭐ **ESSENCIAL**
- `resources/views/livewire/live-stream-broadcast.blade.php` (view atualizada)
- `resources/views/livewire/live-stream-viewer.blade.php` (view atualizada)
- `resources/views/partials/head.blade.php` (CSP atualizado)
- `peerjs-server.js` (servidor local - opcional)
- `peerjs-server-production.js` (servidor otimizado - opcional)
- `package.json` (para instalar dependências - se usar servidor local)

**⚠️ IMPORTANTE:** Os arquivos JavaScript compilados estão em `public/build/` e são carregados automaticamente pelo Vite. NÃO é necessário enviar os arquivos `resources/js/` para produção.

### ✅ **2. Configurar Servidor PeerJS em Produção**

#### **Opção A: Servidor PeerJS no mesmo domínio (Recomendado)**
1. **Upload do servidor:**
   ```
   peerjs-server.js → /public_html/peerjs-server.js
   ```

2. **Instalar dependências no servidor:**
   ```bash
   cd /public_html
   npm install peerjs-server
   ```

3. **Iniciar servidor PeerJS:**
   ```bash
   node peerjs-server.js &
   ```

4. **Configurar para iniciar automaticamente:**
   - Adicionar ao crontab: `@reboot cd /public_html && node peerjs-server.js &`

#### **Opção B: Usar apenas servidores externos**
Se não for possível rodar Node.js no servidor, o sistema usará automaticamente os servidores externos:
- `peerjs-server.herokuapp.com`
- `0.peerjs.com`

### ✅ **3. Configurações de Produção**

#### **Variáveis de Ambiente (.env):**
```env
APP_ENV=production
APP_DEBUG=false
APP_URL=https://www.swingcuritiba.com.br
```

#### **Configurações de HTTPS:**
- Certificado SSL deve estar ativo
- WebRTC requer HTTPS em produção

### ✅ **4. Teste de Conectividade**

#### **URLs para testar:**
1. **Página de transmissão:**
   ```
   https://www.swingcuritiba.com.br/lives/transmitir
   ```

2. **Servidor PeerJS (se configurado):**
   ```
   https://www.swingcuritiba.com.br:9000/peerjs
   ```

3. **Teste de conectividade:**
   - Clicar em "Testar Conexão"
   - Verificar logs no console do navegador

### ✅ **5. Logs Esperados em Produção**

#### **Broadcaster (quem transmite):**
```javascript
🚀 Inicializando streaming service para: live-123
🔄 Tentando servidor 1: www.swingcuritiba.com.br
✅ Peer conectado com ID: live-123 no servidor: www.swingcuritiba.com.br
📹 Câmera iniciada com sucesso
```

#### **Viewer (quem assiste):**
```javascript
🔗 Tentando conectar como viewer ao stream: live-123
🔄 Viewer tentando servidor 1: www.swingcuritiba.com.br
✅ Viewer conectado com ID: viewer-abc123
📹 Stream recebido do broadcaster!
```

### ✅ **6. Troubleshooting Produção**

#### **Problema: "Failed to fetch dynamically imported module"**
**Erro:** `GET https://www.swingcuritiba.com.br/resources/js/streaming-service.js net::ERR_ABORTED 404`
**Causa:** Sistema tentando importar arquivos da pasta `resources/js/` que não existe em produção
**✅ Solução:**
- Arquivos corrigidos para usar `window.StreamingService` (já disponível globalmente)
- Sistema usa fallback automático para imports dinâmicos
- Assets compilados estão em `public/build/` e são carregados pelo Vite

#### **Problema: "Todos os servidores PeerJS falharam"**
**Soluções:**
1. Verificar se o servidor PeerJS está rodando na porta 9000
2. Verificar firewall do servidor
3. Testar com servidores externos apenas

#### **Problema: "CSP blocking connections"**
**Soluções:**
1. Verificar se o CSP inclui o domínio correto
2. Adicionar exceções no .htaccess se necessário

#### **Problema: "Camera permission denied"**
**Soluções:**
1. Verificar se o site está em HTTPS
2. Orientar usuários sobre permissões do navegador

#### **Problema: "StreamingService não disponível"**
**Soluções:**
1. Verificar se `public/build/` foi enviado corretamente
2. Limpar cache do navegador
3. Verificar se Vite está carregando os assets

### ✅ **7. Monitoramento**

#### **Logs importantes para monitorar:**
- Conexões PeerJS
- Erros de WebRTC
- Performance de streaming
- Número de viewers simultâneos

#### **Métricas de sucesso:**
- ✅ Broadcaster consegue iniciar câmera
- ✅ Viewers conseguem se conectar
- ✅ Stream de vídeo funciona
- ✅ Chat funciona em tempo real

### ✅ **8. Backup e Rollback**

#### **Antes do deploy:**
1. Backup dos arquivos atuais
2. Backup do banco de dados
3. Testar em ambiente de staging

#### **Plano de rollback:**
1. Restaurar arquivos anteriores
2. Reverter mudanças no banco
3. Reiniciar serviços se necessário

## 🔧 **Comandos Úteis**

### **Compilar assets:**
```bash
npm run build
```

### **Verificar servidor PeerJS:**
```bash
curl https://www.swingcuritiba.com.br:9000/peerjs
```

### **Logs do servidor:**
```bash
tail -f /var/log/nodejs/peerjs.log
```

## 📞 **Suporte**

Em caso de problemas:
1. Verificar logs do console do navegador
2. Testar conectividade de rede
3. Verificar configurações de firewall
4. Contatar suporte técnico do hosting se necessário

---

**✅ Deploy concluído com sucesso quando:**
- Página de transmissão carrega sem erros
- Teste de conectividade passa
- Broadcaster consegue iniciar live
- Viewers conseguem assistir
- Chat funciona corretamente
