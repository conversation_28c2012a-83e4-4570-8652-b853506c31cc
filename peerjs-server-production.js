/**
 * Servidor <PERSON>eerJ<PERSON> para Produção - www.swingcuritiba.com.br
 * Configurado para ambiente de produção com HTTPS
 */

import { PeerServer } from 'peerjs-server';
import fs from 'fs';
import https from 'https';

// Configurações para produção
const config = {
    port: 9000,
    path: '/peerjs',
    key: 'peerjs',
    allow_discovery: true,
    corsOptions: {
        origin: [
            'https://www.swingcuritiba.com.br',
            'https://swingcuritiba.com.br'
        ],
        credentials: true
    },
    // Configurações otimizadas para produção
    config: {
        iceServers: [
            { urls: 'stun:stun.l.google.com:19302' },
            { urls: 'stun:stun1.l.google.com:19302' },
            { urls: 'stun:stun2.l.google.com:19302' },
            { urls: 'stun:stun3.l.google.com:19302' },
            { urls: 'stun:stun4.l.google.com:19302' }
        ]
    },
    // Configurações de performance
    alive_timeout: 60000,
    key_length: 32,
    ip_limit: 5000,
    concurrent_limit: 5000
};

// Tentar usar HTTPS se certificados estiverem disponíveis
let server;
try {
    // Verificar se existem certificados SSL
    const sslOptions = {
        key: fs.readFileSync('/path/to/ssl/private.key'),
        cert: fs.readFileSync('/path/to/ssl/certificate.crt')
    };
    
    // Criar servidor HTTPS
    server = https.createServer(sslOptions);
    console.log('🔒 Servidor HTTPS configurado');
} catch (error) {
    console.log('⚠️ Certificados SSL não encontrados, usando HTTP');
    server = null;
}

// Inicializar servidor PeerJS
const peerServer = PeerServer(config, server);

// Event listeners
peerServer.on('connection', (client) => {
    console.log('🔗 Cliente conectado:', client.getId());
    console.log('📊 Total de clientes:', peerServer._clients.size);
});

peerServer.on('disconnect', (client) => {
    console.log('❌ Cliente desconectado:', client.getId());
    console.log('📊 Total de clientes:', peerServer._clients.size);
});

peerServer.on('error', (error) => {
    console.error('❌ Erro no servidor PeerJS:', error);
});

// Logs de inicialização
console.log('🚀 Servidor PeerJS para PRODUÇÃO iniciado');
console.log('🌐 Domínio: www.swingcuritiba.com.br');
console.log('🔌 Porta:', config.port);
console.log('📡 Endpoint:', `https://www.swingcuritiba.com.br:${config.port}${config.path}`);
console.log('🔧 CORS permitido para:', config.corsOptions.origin);

// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('🛑 Recebido SIGTERM, encerrando servidor...');
    peerServer.close(() => {
        console.log('✅ Servidor PeerJS encerrado');
        process.exit(0);
    });
});

process.on('SIGINT', () => {
    console.log('🛑 Recebido SIGINT, encerrando servidor...');
    peerServer.close(() => {
        console.log('✅ Servidor PeerJS encerrado');
        process.exit(0);
    });
});

// Log de status a cada 5 minutos
setInterval(() => {
    console.log('📊 Status do servidor:');
    console.log(`   - Clientes conectados: ${peerServer._clients ? peerServer._clients.size : 0}`);
    console.log(`   - Uptime: ${Math.floor(process.uptime())} segundos`);
    console.log(`   - Memória: ${Math.round(process.memoryUsage().heapUsed / 1024 / 1024)} MB`);
}, 5 * 60 * 1000);

export default peerServer;
