/**
 * Servidor PeerJS Local para Live Streaming
 * Executa na porta 9000 para evitar conflitos
 */

import { PeerServer } from 'peerjs-server';

const peerServer = PeerServer({
    port: 9000,
    path: '/peerjs',
    key: 'peerjs',
    allow_discovery: true,
    corsOptions: {
        origin: true,
        credentials: true
    },
    // Configurações de ICE servers para melhor conectividade
    config: {
        iceServers: [
            { urls: 'stun:stun.l.google.com:19302' },
            { urls: 'stun:stun1.l.google.com:19302' },
            { urls: 'stun:stun2.l.google.com:19302' },
            { urls: 'stun:stun3.l.google.com:19302' },
            { urls: 'stun:stun4.l.google.com:19302' }
        ]
    }
});

peerServer.on('connection', (client) => {
    console.log('🔗 Cliente conectado:', client.getId());
});

peerServer.on('disconnect', (client) => {
    console.log('❌ Cliente desconectado:', client.getId());
});

peerServer.on('error', (error) => {
    console.error('❌ Erro no servidor PeerJS:', error);
});

console.log('🚀 Servidor PeerJS iniciado na porta 9000');
console.log('📡 Endpoint: http://localhost:9000/peerjs');
console.log('🔧 Para usar no código: host: "localhost", port: 9000, path: "/peerjs"');
