/**
 * Funções para Live Stream Broadcast
 */

// Variáveis globais para o stream
window.localStreamGlobal = null;
window.streamingService = null;

// Função testCamera disponível globalmente
window.testCamera = function () {
    console.log('testCamera chamada');
    if (typeof window.startCameraAdvanced === 'function') {
        return window.startCameraAdvanced();
    } else {
        console.error('startCameraAdvanced não está disponível ainda');
        // Tentar novamente após um pequeno delay
        setTimeout(() => {
            if (typeof window.startCameraAdvanced === 'function') {
                window.startCameraAdvanced();
            }
        }, 100);
    }
}

// Função para iniciar câmera simples
window.startCameraSimple = async function () {
    try {
        console.log('Iniciando câmera...');

        // Verificar se o navegador suporta getUserMedia
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            throw new Error('Seu navegador não suporta acesso à câmera');
        }

        // Solicitar acesso à câmera e microfone
        window.localStreamGlobal = await navigator.mediaDevices.getUserMedia({
            video: true,
            audio: true
        });

        console.log('Stream obtido:', window.localStreamGlobal);
        console.log('Video tracks:', window.localStreamGlobal.getVideoTracks());
        console.log('Audio tracks:', window.localStreamGlobal.getAudioTracks());

        // Encontrar elemento de vídeo e conectar stream
        const video = document.getElementById('localVideo');
        if (video) {
            video.srcObject = window.localStreamGlobal;
            video.muted = true; // Evitar feedback
            console.log('Stream conectado ao elemento de vídeo');
        } else {
            console.error('Elemento de vídeo não encontrado');
        }

        // Mostrar controles
        const controls = document.getElementById('streamControls');
        if (controls) {
            controls.style.display = 'flex';
        }

        // Atualizar status
        updateCameraStatus('Câmera conectada', 'success');

    } catch (error) {
        console.error('Erro ao acessar câmera:', error);
        updateCameraStatus('Erro: ' + error.message, 'error');
    }
}

// Função para iniciar câmera com WebRTC avançado
window.startCameraAdvanced = async function () {
    try {
        console.log('=== Iniciando Câmera Avançada com WebRTC ===');

        // Verificar se o navegador suporta getUserMedia
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            throw new Error('Seu navegador não suporta acesso à câmera');
        }

        // Obter stream ID da página
        const streamIdElement = document.querySelector('[data-stream-id]');
        const streamId = streamIdElement ? streamIdElement.getAttribute('data-stream-id') : 'live-temp-' + Date.now();

        // Inicializar serviço de streaming
        if (!window.streamingService) {
            // Verificar se StreamingService está disponível globalmente
            if (typeof window.StreamingService !== 'undefined') {
                window.streamingService = new window.StreamingService();
            } else {
                // Fallback: tentar import dinâmico
                try {
                    const StreamingService = (await import('./streaming-service.js')).default;
                    window.streamingService = new StreamingService();
                } catch (error) {
                    console.error('❌ Erro ao carregar StreamingService:', error);
                    throw new Error('StreamingService não disponível. Verifique se os assets foram compilados corretamente.');
                }
            }
            await window.streamingService.initialize(streamId);
        }

        // Iniciar câmera
        window.localStreamGlobal = await window.streamingService.startCamera();

        console.log('Stream obtido:', window.localStreamGlobal);

        // Encontrar elemento de vídeo e conectar stream
        const video = document.getElementById('localVideo');
        if (video) {
            video.srcObject = window.localStreamGlobal;
            video.muted = true; // Evitar feedback
            console.log('Stream conectado ao elemento de vídeo');
        } else {
            console.error('Elemento de vídeo não encontrado');
        }

        // Mostrar controles
        const controls = document.getElementById('streamControls');
        if (controls) {
            controls.style.display = 'flex';
        }

        // Atualizar status
        const statusDiv = document.getElementById('camera-status');
        if (statusDiv) {
            statusDiv.textContent = 'Câmera conectada (WebRTC)';
            statusDiv.className = 'absolute top-4 left-4 text-white px-3 py-1 rounded text-sm bg-green-500';
        }

        // Configurar controles
        setupControlsAdvanced();

        return true;

    } catch (error) {
        console.error('Erro ao acessar câmera avançada:', error);
        const statusDiv = document.getElementById('camera-status');
        if (statusDiv) {
            statusDiv.textContent = 'Erro: ' + error.message;
            statusDiv.className = 'absolute top-4 left-4 text-white px-3 py-1 rounded text-sm bg-red-500';
        }
        return false;
    }
}

// Configurar controles avançados
function setupControlsAdvanced() {
    const toggleCamera = document.getElementById('toggleCamera');
    const toggleMicrophone = document.getElementById('toggleMicrophone');
    const startRecording = document.getElementById('startRecording');
    const stopRecording = document.getElementById('stopRecording');

    if (toggleCamera) {
        toggleCamera.addEventListener('click', () => {
            const videoTrack = window.localStreamGlobal ? window.localStreamGlobal.getVideoTracks()[0] : null;
            if (videoTrack) {
                videoTrack.enabled = !videoTrack.enabled;
                toggleCamera.classList.toggle('opacity-50', !videoTrack.enabled);
                console.log('Câmera:', videoTrack.enabled ? 'ligada' : 'desligada');
            }
        });
    }

    if (toggleMicrophone) {
        toggleMicrophone.addEventListener('click', () => {
            const audioTrack = window.localStreamGlobal ? window.localStreamGlobal.getAudioTracks()[0] : null;
            if (audioTrack) {
                audioTrack.enabled = !audioTrack.enabled;
                toggleMicrophone.classList.toggle('opacity-50', !audioTrack.enabled);
                console.log('Microfone:', audioTrack.enabled ? 'ligado' : 'desligado');
            }
        });
    }

    // Controles de gravação
    if (startRecording && window.streamingService) {
        startRecording.addEventListener('click', async () => {
            try {
                await window.streamingService.startRecording();
                startRecording.style.display = 'none';
                if (stopRecording) stopRecording.style.display = 'block';
                console.log('Gravação iniciada');
            } catch (error) {
                console.error('Erro ao iniciar gravação:', error);
            }
        });
    }

    if (stopRecording && window.streamingService) {
        stopRecording.addEventListener('click', async () => {
            try {
                await window.streamingService.stopRecording();
                stopRecording.style.display = 'none';
                if (startRecording) startRecording.style.display = 'block';
                console.log('Gravação parada');
            } catch (error) {
                console.error('Erro ao parar gravação:', error);
            }
        });
    }
}

// Função para iniciar streaming real
window.startStreamingReal = async function () {
    try {
        if (!window.streamingService) {
            throw new Error('Serviço de streaming não inicializado');
        }

        await window.streamingService.startStreaming();
        await window.streamingService.startRecording();

        console.log('Streaming real iniciado');
        return true;
    } catch (error) {
        console.error('Erro ao iniciar streaming real:', error);
        return false;
    }
}

// Função para parar streaming real
window.stopStreamingReal = async function () {
    try {
        if (window.streamingService) {
            await window.streamingService.stopStreaming();
        }

        console.log('Streaming real parado');
        return true;
    } catch (error) {
        console.error('Erro ao parar streaming real:', error);
        return false;
    }
}

// Função para parar câmera
window.stopCameraSimple = function () {
    try {
        if (window.localStreamGlobal) {
            window.localStreamGlobal.getTracks().forEach(track => {
                track.stop();
            });
            window.localStreamGlobal = null;
            console.log('Câmera parada');
        }

        // Limpar elemento de vídeo
        const video = document.getElementById('localVideo');
        if (video) {
            video.srcObject = null;
        }

        // Esconder controles
        const controls = document.getElementById('streamControls');
        if (controls) {
            controls.style.display = 'none';
        }

        // Atualizar status
        updateCameraStatus('Câmera desconectada', 'info');
    } catch (error) {
        console.error('Erro ao parar câmera:', error);
    }
}

// Função para testar conectividade WebRTC
window.testWebRTCConnection = async function () {
    try {
        console.log('🧪 Testando conectividade WebRTC...');

        const statusDiv = document.getElementById('camera-status');
        if (statusDiv) {
            statusDiv.textContent = 'Testando conectividade...';
            statusDiv.className = 'absolute top-4 left-4 text-white px-3 py-1 rounded text-sm bg-blue-500';
        }

        // Testar acesso à câmera
        const stream = await navigator.mediaDevices.getUserMedia({ video: true, audio: true });
        console.log('✅ Câmera/microfone acessíveis');

        // Testar conexão PeerJS
        let StreamingService;
        if (typeof window.StreamingService !== 'undefined') {
            StreamingService = window.StreamingService;
        } else {
            // Fallback: tentar import dinâmico
            try {
                StreamingService = (await import('./streaming-service.js')).default;
            } catch (error) {
                console.error('❌ Erro ao carregar StreamingService:', error);
                throw new Error('StreamingService não disponível. Verifique se os assets foram compilados corretamente.');
            }
        }

        const testService = new StreamingService();
        const testId = 'test-' + Date.now();

        await testService.initialize(testId);
        console.log('✅ Servidor PeerJS acessível');

        // Limpar teste
        stream.getTracks().forEach(track => track.stop());
        testService.destroy();

        if (statusDiv) {
            statusDiv.textContent = '✅ Conectividade OK - WebRTC funcionando!';
            statusDiv.className = 'absolute top-4 left-4 text-white px-3 py-1 rounded text-sm bg-green-500';
        }

        console.log('🎉 Teste de conectividade concluído com sucesso!');

    } catch (error) {
        console.error('❌ Teste de conectividade falhou:', error);

        const statusDiv = document.getElementById('camera-status');
        if (statusDiv) {
            statusDiv.textContent = '❌ Erro de conectividade: ' + error.message;
            statusDiv.className = 'absolute top-4 left-4 text-white px-3 py-1 rounded text-sm bg-red-500';
        }
    }
}

// Função auxiliar para atualizar status da câmera
function updateCameraStatus(message, type) {
    const statusDiv = document.getElementById('camera-status');
    if (statusDiv) {
        statusDiv.textContent = message;

        let className = 'absolute top-4 left-4 text-white px-3 py-1 rounded text-sm ';
        switch (type) {
            case 'success':
                className += 'bg-green-500';
                break;
            case 'error':
                className += 'bg-red-500';
                break;
            case 'info':
                className += 'bg-blue-500';
                break;
            default:
                className += 'bg-gray-500';
        }

        statusDiv.className = className;
    }
}
