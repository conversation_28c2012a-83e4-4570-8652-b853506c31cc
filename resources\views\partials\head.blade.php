<meta charset="utf-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
<meta name="csrf-token" content="{{ csrf_token() }}">

<!-- Content Security Policy otimizado para WebRTC -->
<meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' https: data:; connect-src 'self' https: wss: ws: https://0.peerjs.com wss://0.peerjs.com https://1.peerjs.com wss://1.peerjs.com https://swingcuritiba.com.br wss://swingcuritiba.com.br https://desiree2.test wss://desiree2.test https://stun.l.google.com:19302; media-src 'self' blob: data: mediastream:; img-src 'self' https: data: blob:; font-src 'self' https: data:; style-src 'self' 'unsafe-inline' https:; script-src 'self' 'unsafe-inline' 'unsafe-eval' https:;">

<title>{{ $title ?? 'Desiree Swing Club - Curitiba' }}</title>

<link rel="preconnect" href="https://fonts.bunny.net">
<link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />

@vite(['resources/css/app.css', 'resources/js/app.js'])

<!-- CSS personalizado para galeria -->
<link rel="stylesheet" href="{{ asset('css/gallery-animations.css') }}">

<!-- Scripts personalizados -->
@auth
<!-- Script de geolocalização automática -->
<script src="{{ asset('js/auto-geolocation.js') }}"></script>
@endauth
