<x-layouts.app :title="'Teste Live'">
    <div class="container mx-auto p-6">
        <h1 class="text-2xl font-bold mb-4 text-gray-300">Teste do Sistema de Lives</h1>

        <div class="space-y-4">
            <button onclick="testCameraSimple()" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                Testar Câmera Simples
            </button>

            <div id="simple-status" class="text-gray-400"></div>

            <video id="simple-video" autoplay muted playsinline class="w-96 h-64 bg-black border rounded"></video>
        </div>

        <div class="mt-8">
            <a href="{{ route('live-streams.index') }}" class="text-blue-500 hover:underline">
                Ir para Lives
            </a>
        </div>
    </div>

    <script>
        async function testCameraSimple() {
            const status = document.getElementById('simple-status');
            const video = document.getElementById('simple-video');

            status.textContent = 'Testando câmera...';
            console.log('=== Teste Simples de Câmera ===');

            try {
                const stream = await navigator.mediaDevices.getUserMedia({ video: true, audio: false });
                video.srcObject = stream;
                status.textContent = 'Câmera funcionando!';
                status.className = 'text-green-400';
                console.log('Câmera funcionando!');
            } catch (error) {
                status.textContent = 'Erro: ' + error.message;
                status.className = 'text-red-400';
                console.error('Erro:', error);
            }
        }
    </script>
</x-layouts.app>
